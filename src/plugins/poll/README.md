# 投票系统插件

这是一个为 nonebot2 开发的投票系统插件，支持多种类型的群投票功能。

## 功能特性

- 🔐 **权限控制**：只有超级管理员(SUPERUSER)可以创建投票
- 📊 **多种投票类型**：
  - 二选一投票（是/否）
  - 单选投票（多个选项选一个）
  - 多选投票（多个选项选多个）
- ⏰ **自动截止**：支持设置投票持续时间，到期自动结束并推送结果
- 🔄 **实时同步**：所有配置群的投票结果统一计算
- 💾 **数据持久化**：使用 SQLite 数据库存储投票数据

## 配置方法

在你的`.env`文件中添加目标群列表：

```env
# 投票插件配置
TARGET_GROUPS=[726432071, 753696694, 650536599]  # 替换为你的群号列表
```

## 使用方法

### 管理员操作（私聊）

1. **创建投票**

   ```
   poll create
   ```

   然后按照向导步骤设置：

   - 投票标题
   - 投票类型（二选一/单选/多选）
   - 投票选项（如适用）
   - 投票描述（可选）
   - 持续时间（如：30m、2h、1d）

2. **查看所有投票**

   ```
   poll list
   ```

3. **查看投票结果**

   ```
   poll result <投票ID>
   ```

4. **手动结束投票**
   ```
   poll end <投票ID>
   ```

### 群成员操作（群聊）

1. **查看当前投票**

   ```
   poll
   ```

2. **参与投票**

   **二选一投票：**

   ```
   poll y    # 投"是"
   poll n    # 投"否"
   ```

   **单选投票：**

   ```
   poll 1    # 选择第1个选项
   poll 2    # 选择第2个选项
   ```

   **多选投票：**

   ```
   poll 1 3 5    # 选择第1、3、5个选项
   ```

3. **多投票时指定 ID**
   ```
   poll <投票ID> <选择>
   ```

## 投票类型说明

### 二选一投票

- 固定为"是"和"否"两个选项
- 群成员回复 `y`、`yes`、`是`、`1` 表示赞成
- 群成员回复 `n`、`no`、`否`、`0` 表示反对

### 单选投票

- 可自定义多个选项（2-10 个）
- 每人只能选择一个选项
- 回复选项对应的数字编号

### 多选投票

- 可自定义多个选项（2-10 个）
- 每人可以选择多个选项
- 回复多个数字编号，用空格分隔

## 时间格式

支持以下时间格式：

- `30m` - 30 分钟
- `2h` - 2 小时
- `1d` - 1 天
- `7d` - 7 天

限制：

- 最短 1 分钟，最长 30 天

## 数据库

插件会在插件目录下自动创建`polls.db`文件存储数据，包含：

- 投票基本信息
- 用户投票记录
- 自动处理重复投票（允许修改投票）

## 注意事项

1. 投票结果在所有配置群之间共享
2. 用户可以修改自己的投票选择
3. 投票到期后会自动推送结果到所有群
4. 只有在配置的目标群中才能参与投票
5. 建议定期备份数据库文件
