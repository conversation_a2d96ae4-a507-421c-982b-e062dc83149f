import httpx
import base64
import openai

BASE_URL = "https://models.inference.ai.azure.com"
API_KEY = "****************************************"

def transfer_pdf_to_base64(pdf_path: str) -> str:
    with open(pdf_path, 'rb') as pdf_file:
        pdf_bytes = pdf_file.read()
    return base64.b64encode(pdf_bytes).decode('utf-8')

def get_pdf_url(pdf_path: str) -> str:
    pdf_base64 = transfer_pdf_to_base64(pdf_path)
    return f"data:application/pdf;base64,{pdf_base64}"

http_client = httpx.Client(proxy="http://127.0.0.1:7890")
client = openai.Client(base_url=BASE_URL, api_key=API_KEY, http_client=http_client)

response = client.chat.completions.create(
    model="DeepSeek-R1",
    messages=[
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "什么是llm"
        }
    ],
)
if __name__ == "__main__":
    print(response.choices[0].message.content)
