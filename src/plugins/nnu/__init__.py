from nonebot import on_command
from nonebot.plugin import PluginMetadata
from nonebot.params import CommandArg
from nonebot.adapters.onebot.v11 import Bot, Event, Message
from .config import Config
from ehall import CourseUser

import sqlite3

__plugin_meta = PluginMetadata(
    name="nnu",
    description="",
    usage="",
    config=Config,
)

# 连接数据库
conn = sqlite3.connect('database/users.db')
c = conn.cursor()

# 创建用户表
c.execute('''
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    userid TEXT NOT NULL,
    username TEXT NOT NULL,
    password TEXT NOT NULL
)
''')
conn.commit()

users = {}

login = on_command("登录", priority=5, block=True)
coursebatch = on_command("选课批次", priority=5, block=True)
user_info = on_command("信息", priority=5, block=True)
select_info = on_command("选课信息", priority=5, block=True)


@login.handle()
async def handle(bot: Bot, event: Event, args: Message = CommandArg()):
    userid = event.get_user_id()
    # 发送格式 /南师登录 用户名 密码
    args = str(args).split()
    if len(args) != 2:
        await login.finish("请发送正确的格式：/南师登录 用户名 密码")
        return
    username, password = args
    try:
        user = CourseUser(username, password)
        users[userid] = user
    except Exception as e:
        await login.finish(f"发生错误：{str(e)}")
        return
    # 将用户信息保存到数据库，如果已在数据库中则更新密码，如果不在则插入
    # 查询用户
    c.execute('SELECT * FROM users WHERE userid = ?', (userid,))
    if c.fetchone():
        # 更新用户信息
        c.execute('UPDATE users SET username = ?, password = ? WHERE userid = ?', (username, password, userid))
        conn.commit()
        await login.finish("更新用户信息成功")
    else:
        # 插入新用户
        c.execute('INSERT INTO users (userid, username, password) VALUES (?, ?, ?)', (userid, username, password))
        conn.commit()
        await login.finish("登录成功")


@coursebatch.handle()
async def handle(bot: Bot, event: Event):
    try:
        batch_info = CourseUser.get_batch_information()
    except Exception as e:
        await coursebatch.finish(f"发生错误：{str(e)}")
        return
    await coursebatch.finish(f"当前选课批次名称：{batch_info['batchName']}\n"
                             f"当前选课批次类型：{batch_info['type']}\n"
                             f"学期：{batch_info['schoolTerm']}\n"
                             f"开始时间：{batch_info['beginTime']}\n"
                             f"结束时间：{batch_info['endTime']}")


@user_info.handle()
async def handle(bot: Bot, event: Event):
    userid = event.get_user_id()
    c.execute('SELECT * FROM users WHERE userid = ?', (userid,))
    user = c.fetchone()
    if not user:
        await user_info.finish("Sanuki没有你的登录信息，请先发送 /南师登录 用户名 密码 登录")
        return
    if userid in users:
        user = users[userid]
    else:
        username, password = user[2], user[3]
        user = CourseUser(username, password)
    try:
        userinfo = user.get_user_info()
    except Exception as e:
        await user_info.finish(f"发生错误：{str(e)}")
        return
    await user_info.finish(f"姓名：{userinfo['name']}\n"
                           f"学号：{userinfo['code']}\n"
                           f"学院：{userinfo['college']}\n"
                           f"专业：{userinfo['department']}\n"
                           f"当前选课批次：{userinfo['current_elective']}")


@select_info.handle()
async def handle(bot: Bot, event: Event):
    userid = event.get_user_id()
    c.execute('SELECT * FROM users WHERE userid = ?', (userid,))
    user = c.fetchone()
    if not user:
        await select_info.finish("Sanuki没有你的登录信息，请先发送 /南师登录 用户名 密码 登录")
        return
    if userid in users:
        user = users[userid]
    else:
        username, password = user[2], user[3]
        user = CourseUser(username, password)
    try:
        course_info = user.get_selected_course()
    except Exception as e:
        await select_info.finish(f"发生错误：{str(e)}")
        return

    msg = f"你当前共选了{len(course_info)}门课\n"
    for i, course in enumerate(course_info):
        msg += f"{i+1}. {course['name']}\n" \
               f"选课id: {course['id']}\n" \
               f"教学老师: {course['teacher']}\n" \
               f"课程类型：{course['nature']} {course['type']}\n"
        if i % 5 == 4:
            await select_info.send(msg)
            msg = ""
    if msg:
        await select_info.finish(msg)

