import base64
import httpx
import mimetypes
from nonebot.params import CommandArg

from nonebot.adapters.onebot.v11 import (
    Bot,
    Event,
    Message,
    MessageSegment,
    MessageEvent, GroupMessageEvent, PrivateMessageEvent,
)
from nonebot import get_driver, on_message, logger, on_command

reci = on_command("reci", priority=10)

@reci.handle()
async def handle_reci(bot: Bot, event: GroupMessageEvent):
    if event.sender.user_id != 1578034713:
        return
    reply = event.reply
    message = reply.message
    if reply:
        for seg in message:
            if seg.type == "text":
                text = seg.data["text"]
                logger.info(f'文本: {text}')
            if seg.type in ['image', 'video']:
                file_url = seg.data.get('url') or seg.data.get('file_url')
                file_type = seg.type
                data_url = await get_file_url(file_url, file_type)
                if data_url:
                    logger.info(f'b64: {data_url}')
            if seg.type == 'file':
                file_id = seg.data.get('file_id')
                file_url_info = await bot.call_api(
                    "get_group_file_url",
                    file_id=file_id,
                    group_id=event.group_id, 
                )
                url = file_url_info["url"]
                data_url = await get_file_url(url, 'file')
                if data_url:
                    logger.info(f'url: {url}')
        await reci.finish('完毕')
    
async def get_file_url(file_url: str, file_type: str) -> str:
    type_map = {
        'image': 'image/png',
        'video': 'video/mp4',
        'file': 'application/pdf',
    }
    try:    
        async with httpx.AsyncClient() as client:
            response = await client.get(file_url)
            response.raise_for_status()
            file_data = response.content
            file_base64 = base64.b64encode(file_data).decode("utf-8")
            # check if the file is too large(>20MB)
        if len(file_base64) > 20 * 1024 * 1024:
            return None
        # supported file
        # image: data:image/png;base64,
        # video: video/mp4;base64,
        # file: application/pdf;base64,
        return f"data:{type_map[file_type]};base64,{file_base64}"
    except Exception as e:
        logger.error(f'获取聊天文件失败: {e}')
        return None
