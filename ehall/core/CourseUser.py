import requests
from ehall.utils.course import login
from ehall.utils.request import get_timestamp, default_header
from ehall.utils.url import get_course_url
from cachetools import TTLCache

cache = TTLCache(maxsize=1024, ttl=4800)
url_base = get_course_url()


class CourseUser:

    @staticmethod
    def get_batch_information():
        s = requests.session()
        s.headers.update(default_header)
        # GET /xsxkapp/sys/xsxkapp/elective/batch.do?timestamp=1723347044631
        res = s.get(url_base + '/xsxkapp/sys/xsxkapp/elective/batch.do?timestamp=' + get_timestamp())
        if res.status_code != 200:
            raise Exception("Failed to get batch information.")
        result = [
            {
                'batchCode': item['code'],
                'batchName': item['name'],
                'type': item['typeName'],
                'schoolTerm': item['schoolTerm'],
                'beginTime': item['beginTime'],
                'endTime': item['endTime']
            }
            for item in res.json()['dataList']
        ]
        return result

    def __init__(self, username, password):
        self.batch = None
        self.username = username
        self.password = password

    def refresh(self):
        if self.username is not None and self.password is not None:
            session, token = login(self.username, self.password)
            self.batch = self.get_batch_information()
            # 将session与token缓存
            cache[self.username] = session
            cache[self.username + '_token'] = token

    def get_session(self):
        if self.username in cache:
            session = cache[self.username]
            # insert token into session header(Token : token)
            session.headers['Token'] = cache[self.username + '_token']
            return session
        else:
            self.refresh()
            session = cache[self.username]
            session.headers['Token'] = cache[self.username + '_token']
            return session

    def get_user_info(self):
        session = self.get_session()
        res = session.get(url_base + f'/xsxkapp/sys/xsxkapp/student/{self.username}.do?timestamp={get_timestamp()}')
        if res.status_code != 200:
            raise Exception("Failed to get user information.")
        result = {'name': res.json()['data']['name'], 'code': res.json()['data']['code'],
                  'college': res.json()['data']['collegeName'], 'department': res.json()['data']['departmentName'],
                  'current_elective': res.json()['data']['electiveBatchList'][0]['name'] if res.json()['data'][
                      'electiveBatchList'] else "当前无选课批次"}
        return result

    def get_selected_course(self):
        session = self.get_session()
        params = {
            'timestamp': get_timestamp(),
            'studentCode': self.username,
            'electiveBatchCode': self.batch['batchCode']
        }
        res = session.get(url_base + f'/xsxkapp/sys/xsxkapp/elective/courseResult.do', params=params)
        if res.status_code != 200:
            raise Exception("Failed to get selected course information.")
        result = []
        for course in res.json()['dataList']:
            result.append(
                {'id': course['teachingClassID'], 'name': course['courseName'], 'teacher': course['teacherName'],
                 'nature': course['courseNatureName'], 'type': course['courseTypeName']})
        return result

    def select_course(self, course_id):
        batch_code = self.batch['batchCode']

    def get_recommend_course(self):
        pass
