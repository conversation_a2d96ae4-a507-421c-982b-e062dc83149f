import ddddocr
import requests
from ehall.utils.request import default_header, get_timestamp
from ehall.utils.url import get_course_url

url_base = get_course_url()
ocr = ddddocr.DdddOcr(show_ad=False)
ocr.set_ranges(5)


def pick_verified_captcha():
    vtoken, image = get_captcha_image()
    if image is None:
        return None
    result = get_captcha_text(image)
    # if the digit is not 4, retry
    if len(result) != 4:
        return pick_verified_captcha()
    # 直接校验是否有效
    params = {
        'timestrap': get_timestamp(),
        'loginName': "1111",
        'loginPwd': "222",
        'verifyCode': result,
        'vtoken': vtoken
    }
    s = requests.Session()
    s.headers.update(default_header)
    res = s.get(url_base + '/xsxkapp/sys/xsxkapp/student/check/login.do', params=params, verify=False)
    # 如果是验证码错了，响应如下：{"data":null,"code":"3","msg":"验证码不正确","timestamp":"0","map":null}
    if res.json()['code'] == '3':
        return pick_verified_captcha()
    return vtoken, result


def get_captcha_text(img):
    result = ocr.classification(img)
    return result


def get_captcha_image():
    vcode_url = url_base + '/xsxkapp/sys/xsxkapp/student/4/vcode.do?timestamp=' + get_timestamp()
    s = requests.Session()
    s.headers.update(default_header)
    res = s.get(vcode_url, verify=False)
    if res.status_code != 200:
        return None
    token = res.json()['data']['token']
    image_url = url_base + f'/xsxkapp/sys/xsxkapp/student/vcode/image.do?vtoken={token}'
    s.headers['Accept'] = ('image/webp,image/avif,image/jxl,image/heic,image/heic-sequence,video/*;q=0.8,image/png,'
                           'image/svg+xml,image/*;q=0.8,*/*;q=0.5')
    s.headers['Sec-Fetch-Dest'] = 'image'
    res = s.get(image_url, verify=False)
    if res.status_code != 200:
        return None
    return token, res.content
