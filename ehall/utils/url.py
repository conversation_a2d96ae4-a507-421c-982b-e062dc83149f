def get_cas_url() -> str:
    """return the CAS URL for the specified school."""
    return ('https://authserver.nnu.edu.cn/authserver/login?service=https%3A%2F%2Fehall.nnu.edu.cn%2Flogin'
            '%3Fservice%3Dhttp%3A%2F%2Fehall.nnu.edu.cn%2Fywtb-portal%2Fstandard%2Findex.html%23%2FWorkBench'
            '%2Fworkbench')


def get_ehall_url() -> str:
    """return the eHall URL for the specified school."""
    return 'https://ehall.nnu.edu.cn'


def get_ehallapp_url() -> str:
    """return the eHallApp URL for the specified school."""
    return 'https://ehallapp.nnu.edu.cn'


def get_course_url() -> str:
    """return the course select URL for the specified school."""
    return 'https://xsxk.nnu.edu.cn'
