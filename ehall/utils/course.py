import requests
import execjs
import base64

from requests import Session

from ehall.utils.url import get_course_url
from ehall.utils.captcha import pick_verified_captcha
from ehall.utils.request import get_timestamp, default_header
import importlib.resources as pkg_resources

url_base = get_course_url()
with pkg_resources.open_text('ehall.assets', 'des.js') as f:
    js = f.read()
ctx = execjs.compile(js)


def login(username: str, password: str) -> Session | tuple[Session, str]:
    vtoken, captcha = pick_verified_captcha()
    enc_password = encrypt_password(password)
    params = {
        'timestrap': get_timestamp(),
        'loginName': username,
        'loginPwd': enc_password,
        'verifyCode': captcha,
        'vtoken': vtoken
    }
    s = requests.Session()
    s.headers.update(default_header)
    res = s.get(url_base + '/xsxkapp/sys/xsxkapp/student/check/login.do', params=params, verify=False)
    if res.status_code != 200:
        raise Exception("Failed to login. Please check your username and password.")
    # 成功响应：{"data":{"loginTime":"1723347070845","token":"fad7f591-5205-4862-a426-450fa28d320d","number":"123123","name":"哈哈哈","lastVisitTime":1723347070845,"wid":null},"code":"1","msg":"登录成功","timestamp":"1","map":null}
    token = res.json()['data']['token']
    return s, token


def encrypt_password(password: str) -> str:
    key1 = "this"
    key2 = "password"
    key3 = "is"
    result = ctx.call('strEnc', password, key1, key2, key3)
    return base64.b64encode(result.encode()).decode()


